<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RPA Test Sida</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }

        h1 {
            color: #2c3e50;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: 300;
            font-size: 2.5rem;
        }

        .section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background-color: #fdfdfd;
        }

        .section h2 {
            color: #495057;
            margin-bottom: 1rem;
            font-weight: 400;
            font-size: 1.3rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #6c757d;
            font-weight: 500;
        }

        input[type="text"],
        input[type="email"],
        input[type="password"],
        input[type="number"],
        select,
        textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        input:focus,
        select:focus,
        textarea:focus {
            outline: none;
            border-color: #007bff;
        }

        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #0056b3;
        }

        button.secondary {
            background-color: #6c757d;
        }

        button.secondary:hover {
            background-color: #545b62;
        }

        button.success {
            background-color: #28a745;
        }

        button.success:hover {
            background-color: #1e7e34;
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .radio-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 500;
            color: #495057;
        }

        .status-message {
            padding: 1rem;
            border-radius: 6px;
            margin-top: 1rem;
            display: none;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .file-upload {
            border: 2px dashed #dee2e6;
            padding: 2rem;
            text-align: center;
            border-radius: 6px;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        .file-upload:hover {
            border-color: #007bff;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 1rem;
        }

        .progress-fill {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>RPA Test Sida</h1>

        <!-- Formulär sektion -->
        <div class="section">
            <h2>Användarformulär</h2>
            <form id="userForm">
                <div class="form-group">
                    <label for="firstName">Förnamn:</label>
                    <input type="text" id="firstName" name="firstName" placeholder="Ange ditt förnamn">
                </div>
                
                <div class="form-group">
                    <label for="lastName">Efternamn:</label>
                    <input type="text" id="lastName" name="lastName" placeholder="Ange ditt efternamn">
                </div>
                
                <div class="form-group">
                    <label for="email">E-post:</label>
                    <input type="email" id="email" name="email" placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="age">Ålder:</label>
                    <input type="number" id="age" name="age" min="1" max="120" placeholder="25">
                </div>
                
                <div class="form-group">
                    <label for="country">Land:</label>
                    <select id="country" name="country">
                        <option value="">Välj land</option>
                        <option value="se">Sverige</option>
                        <option value="no">Norge</option>
                        <option value="dk">Danmark</option>
                        <option value="fi">Finland</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="comments">Kommentarer:</label>
                    <textarea id="comments" name="comments" rows="4" placeholder="Skriv dina kommentarer här..."></textarea>
                </div>
                
                <button type="submit" id="submitBtn">Skicka formulär</button>
                <button type="button" id="clearBtn" class="secondary">Rensa</button>
            </form>
        </div>

        <!-- Knappar sektion -->
        <div class="section">
            <h2>Interaktiva element</h2>
            
            <div class="form-group">
                <label>Hobbyer (checkboxar):</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="hobby1" name="hobbies" value="läsning">
                        <label for="hobby1">Läsning</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="hobby2" name="hobbies" value="sport">
                        <label for="hobby2">Sport</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="hobby3" name="hobbies" value="musik">
                        <label for="hobby3">Musik</label>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label>Favoritfärg (radioknappar):</label>
                <div class="radio-group">
                    <div class="radio-item">
                        <input type="radio" id="color1" name="color" value="blå">
                        <label for="color1">Blå</label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" id="color2" name="color" value="röd">
                        <label for="color2">Röd</label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" id="color3" name="color" value="grön">
                        <label for="color3">Grön</label>
                    </div>
                </div>
            </div>
            
            <button id="alertBtn">Visa varning</button>
            <button id="confirmBtn" class="secondary">Bekräfta åtgärd</button>
            <button id="progressBtn" class="success">Starta progress</button>
        </div>

        <!-- Filnedladdning sektion -->
        <div class="section">
            <h2>Filnedladdning</h2>

            <div class="form-group">
                <label>Nedladdningsalternativ:</label>
                <button id="downloadBtn" class="success">Ladda ner PDF-fil</button>
                <button id="openBtn" class="secondary">Öppna PDF i ny flik</button>
            </div>

            <div class="form-group">
                <p style="color: #6c757d; font-size: 0.9rem; margin-top: 0.5rem;">
                    <strong>Ladda ner:</strong> Startar automatisk nedladdning av filen<br>
                    <strong>Öppna i ny flik:</strong> Öppnar filen i webbläsaren utan att ladda ner
                </p>
            </div>
        </div>

        <!-- Tabell sektion -->
        <div class="section">
            <h2>Datatabell</h2>
            <table id="dataTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Namn</th>
                        <th>E-post</th>
                        <th>Status</th>
                        <th>Åtgärd</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>001</td>
                        <td>Anna Andersson</td>
                        <td><EMAIL></td>
                        <td>Aktiv</td>
                        <td><button class="secondary">Redigera</button></td>
                    </tr>
                    <tr>
                        <td>002</td>
                        <td>Erik Eriksson</td>
                        <td><EMAIL></td>
                        <td>Inaktiv</td>
                        <td><button class="secondary">Redigera</button></td>
                    </tr>
                    <tr>
                        <td>003</td>
                        <td>Maria Nilsson</td>
                        <td><EMAIL></td>
                        <td>Aktiv</td>
                        <td><button class="secondary">Redigera</button></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Progress bar -->
        <div class="progress-bar" id="progressBar" style="display: none;">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <!-- Status meddelanden -->
        <div id="statusMessage" class="status-message"></div>
    </div>

    <script>
        // Formulär hantering
        document.getElementById('userForm').addEventListener('submit', function(e) {
            e.preventDefault();
            showMessage('Formulär skickat framgångsrikt!', 'success');
        });

        document.getElementById('clearBtn').addEventListener('click', function() {
            document.getElementById('userForm').reset();
            showMessage('Formulär rensat!', 'success');
        });

        // Knapp funktioner
        document.getElementById('alertBtn').addEventListener('click', function() {
            alert('Detta är en testvarning för RPA!');
        });

        document.getElementById('confirmBtn').addEventListener('click', function() {
            if (confirm('Är du säker på att du vill fortsätta?')) {
                showMessage('Åtgärd bekräftad!', 'success');
            } else {
                showMessage('Åtgärd avbruten!', 'error');
            }
        });

        document.getElementById('progressBtn').addEventListener('click', function() {
            startProgress();
        });

        // Filnedladdning funktioner
        document.getElementById('downloadBtn').addEventListener('click', function() {
            const link = document.createElement('a');
            link.href = 'https://tinocojohnsson.se/dagsavslut_kassa_momsuppdelning.pdf';
            link.download = 'dagsavslut_kassa_momsuppdelning.pdf';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            showMessage('Nedladdning startad!', 'success');
        });

        document.getElementById('openBtn').addEventListener('click', function() {
            window.open('https://tinocojohnsson.se/dagsavslut_kassa_momsuppdelning.pdf', '_blank');
            showMessage('Fil öppnad i ny flik!', 'success');
        });

        // Progress bar funktion
        function startProgress() {
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            
            progressBar.style.display = 'block';
            progressFill.style.width = '0%';
            
            let progress = 0;
            const interval = setInterval(function() {
                progress += 10;
                progressFill.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(function() {
                        progressBar.style.display = 'none';
                        showMessage('Process slutförd!', 'success');
                    }, 500);
                }
            }, 200);
        }

        // Status meddelande funktion
        function showMessage(message, type) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.textContent = message;
            statusDiv.className = 'status-message status-' + type;
            statusDiv.style.display = 'block';
            
            setTimeout(function() {
                statusDiv.style.display = 'none';
            }, 3000);
        }

        // Tabell redigera knappar
        document.querySelectorAll('table button').forEach(function(button) {
            button.addEventListener('click', function() {
                const row = this.closest('tr');
                const name = row.cells[1].textContent;
                showMessage('Redigerar användare: ' + name, 'success');
            });
        });
    </script>
</body>
</html>
